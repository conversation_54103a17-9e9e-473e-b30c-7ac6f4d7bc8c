import React from 'react';
import { Box, Skeleton, Paper } from '@mui/material';

// 测试用例列表骨架屏
export const TestCaseListSkeleton = React.memo(() => (
  <Box sx={{ p: 2 }}>
    {[...Array(5)].map((_, index) => (
      <Paper key={index} elevation={1} sx={{ p: 2, mb: 2 }}>
        <Skeleton variant="text" width="70%" height={32} sx={{ mb: 1 }} />
        <Skeleton variant="text" width="40%" height={20} sx={{ mb: 1 }} />
        <Skeleton variant="rectangular" width="100%" height={80} sx={{ mb: 1 }} />
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Skeleton variant="rectangular" width={60} height={24} />
          <Skeleton variant="rectangular" width={80} height={24} />
        </Box>
      </Paper>
    ))}
  </Box>
));

// 思维导图骨架屏
export const MindMapSkeleton = React.memo(() => (
  <Box sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
    <Skeleton variant="circular" width={80} height={80} sx={{ mb: 3 }} />
    <Skeleton variant="text" width="60%" height={40} sx={{ mb: 2 }} />
    <Skeleton variant="text" width="40%" height={30} sx={{ mb: 3 }} />
    
    <Box sx={{ display: 'flex', justifyContent: 'space-around', width: '100%', mb: 3 }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Skeleton variant="rectangular" width={120} height={80} sx={{ mb: 1 }} />
        <Skeleton variant="text" width={100} height={20} />
      </Box>
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Skeleton variant="rectangular" width={120} height={80} sx={{ mb: 1 }} />
        <Skeleton variant="text" width={100} height={20} />
      </Box>
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Skeleton variant="rectangular" width={120} height={80} sx={{ mb: 1 }} />
        <Skeleton variant="text" width={100} height={20} />
      </Box>
    </Box>
    
    <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
      <Skeleton variant="rectangular" width={80} height={60} />
      <Skeleton variant="rectangular" width={80} height={60} />
      <Skeleton variant="rectangular" width={80} height={60} />
    </Box>
  </Box>
));

// 流式输出骨架屏
export const StreamingSkeleton = React.memo(() => (
  <Box sx={{ p: 2 }}>
    <Skeleton variant="rectangular" width="100%" height={8} sx={{ mb: 2, borderRadius: 1 }} />
    <Box sx={{ bgcolor: '#f5f5f5', borderRadius: 1, p: 2 }}>
      <Skeleton variant="text" width="90%" height={24} sx={{ mb: 1 }} />
      <Skeleton variant="text" width="75%" height={24} sx={{ mb: 1 }} />
      <Skeleton variant="text" width="85%" height={24} sx={{ mb: 1 }} />
      <Skeleton variant="text" width="60%" height={24} sx={{ mb: 2 }} />
      
      <Skeleton variant="rectangular" width="100%" height={120} sx={{ mb: 2 }} />
      
      <Skeleton variant="text" width="80%" height={24} sx={{ mb: 1 }} />
      <Skeleton variant="text" width="70%" height={24} />
    </Box>
  </Box>
));

// 文件上传区域骨架屏
export const UploadAreaSkeleton = React.memo(() => (
  <Box sx={{ p: 3, textAlign: 'center' }}>
    <Skeleton variant="circular" width={60} height={60} sx={{ mx: 'auto', mb: 2 }} />
    <Skeleton variant="text" width="60%" height={32} sx={{ mx: 'auto', mb: 1 }} />
    <Skeleton variant="text" width="40%" height={24} sx={{ mx: 'auto', mb: 2 }} />
    <Skeleton variant="rectangular" width={200} height={40} sx={{ mx: 'auto', borderRadius: 2 }} />
  </Box>
));

// 模型选择骨架屏
export const ModelSelectSkeleton = React.memo(() => (
  <Box>
    <Skeleton variant="text" width={60} height={20} sx={{ mb: 1 }} />
    <Skeleton variant="rectangular" width={200} height={40} sx={{ borderRadius: 2 }} />
    <Skeleton variant="text" width={150} height={16} sx={{ mt: 0.5 }} />
  </Box>
));

// 通用卡片骨架屏
export const CardSkeleton = React.memo(({ lines = 3, showImage = false }) => (
  <Paper elevation={1} sx={{ p: 2 }}>
    {showImage && (
      <Skeleton variant="rectangular" width="100%" height={140} sx={{ mb: 2, borderRadius: 1 }} />
    )}
    <Skeleton variant="text" width="80%" height={28} sx={{ mb: 1 }} />
    {[...Array(lines)].map((_, index) => (
      <Skeleton 
        key={index} 
        variant="text" 
        width={`${Math.random() * 40 + 60}%`} 
        height={20} 
        sx={{ mb: 0.5 }} 
      />
    ))}
  </Paper>
));

// 表格骨架屏
export const TableSkeleton = React.memo(({ rows = 5, columns = 3 }) => (
  <Box>
    {/* 表头 */}
    <Box sx={{ display: 'flex', gap: 2, mb: 1, p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}>
      {[...Array(columns)].map((_, index) => (
        <Skeleton key={index} variant="text" width={`${100/columns}%`} height={24} />
      ))}
    </Box>
    
    {/* 表格行 */}
    {[...Array(rows)].map((_, rowIndex) => (
      <Box key={rowIndex} sx={{ display: 'flex', gap: 2, mb: 1, p: 1 }}>
        {[...Array(columns)].map((_, colIndex) => (
          <Skeleton 
            key={colIndex} 
            variant="text" 
            width={`${100/columns}%`} 
            height={20} 
          />
        ))}
      </Box>
    ))}
  </Box>
));

export default {
  TestCaseListSkeleton,
  MindMapSkeleton,
  StreamingSkeleton,
  UploadAreaSkeleton,
  ModelSelectSkeleton,
  CardSkeleton,
  TableSkeleton
};
