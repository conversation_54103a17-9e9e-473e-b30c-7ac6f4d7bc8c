import React, { useEffect, useRef, useMemo } from 'react';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import LinearProgress from '@mui/material/LinearProgress';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const StreamingOutput = React.memo(({ content }) => {
  const outputRef = useRef(null);

  // 使用useMemo缓存Markdown内容，避免不必要的重新解析
  const memoizedContent = useMemo(() => content, [content]);

  // 当内容更新时自动滚动到底部
  useEffect(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [content]);

  // 防抖滚动，避免频繁滚动影响性能
  useEffect(() => {
    let timeoutId;
    if (outputRef.current && content) {
      timeoutId = setTimeout(() => {
        if (outputRef.current) {
          outputRef.current.scrollTop = outputRef.current.scrollHeight;
        }
      }, 100);
    }
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [content]);

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
      <Box sx={{ mb: 2 }}>
        <LinearProgress />
      </Box>

      <Box
        ref={outputRef}
        sx={{
          backgroundColor: '#f5f5f5',
          borderRadius: 1,
          p: 2,
          maxHeight: '400px',
          overflowY: 'auto',
          wordBreak: 'break-word',
          // 添加硬件加速
          transform: 'translateZ(0)',
          willChange: 'scroll-position'
        }}
      >
        {memoizedContent ? (
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {memoizedContent}
          </ReactMarkdown>
        ) : (
          <Typography variant="body2" color="text.secondary">
            等待输出...
          </Typography>
        )}
      </Box>
    </Paper>
  );
});

export default StreamingOutput;
