import React, { useState, useMemo, useCallback } from 'react';
import { FixedSizeList as List } from 'react-window';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Chip from '@mui/material/Chip';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import Skeleton from '@mui/material/Skeleton';

// 单个测试用例项组件 - 使用React.memo优化
const TestCaseItem = React.memo(({ testCase, index, style }) => {
  const [expanded, setExpanded] = useState(false);

  const handleToggle = useCallback(() => {
    setExpanded(prev => !prev);
  }, []);

  return (
    <div style={style}>
      <Box sx={{ p: 1, mx: 1 }}>
        <Accordion expanded={expanded} onChange={handleToggle}>
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            aria-controls={`panel${index}-content`}
            id={`panel${index}-header`}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {testCase.id || `TC-${index + 1}`}: {testCase.title}
              </Typography>
              {testCase.priority && (
                <Chip
                  label={testCase.priority}
                  size="small"
                  color={testCase.priority === '高' ? 'error' : testCase.priority === '中' ? 'warning' : 'default'}
                />
              )}
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Typography variant="body2" color="text.secondary">
                <strong>描述:</strong> {testCase.description}
              </Typography>

              {testCase.preconditions && (
                <Typography variant="body2" color="text.secondary">
                  <strong>前置条件:</strong> {testCase.preconditions}
                </Typography>
              )}

              {testCase.steps && testCase.steps.length > 0 && (
                <TableContainer component={Paper} elevation={1}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell><strong>#</strong></TableCell>
                        <TableCell><strong>步骤描述</strong></TableCell>
                        <TableCell><strong>预期结果</strong></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {testCase.steps.map((step, stepIndex) => (
                        <TableRow key={stepIndex}>
                          <TableCell>{step.step_number}</TableCell>
                          <TableCell>{step.description}</TableCell>
                          <TableCell>{step.expected_result}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Box>
          </AccordionDetails>
        </Accordion>
      </Box>
    </div>
  );
});

// 骨架屏组件
const TestCaseSkeleton = React.memo(() => (
  <Box sx={{ p: 1, mx: 1 }}>
    <Paper elevation={1} sx={{ p: 2 }}>
      <Skeleton variant="text" width="60%" height={32} />
      <Skeleton variant="text" width="40%" height={20} sx={{ mt: 1 }} />
      <Skeleton variant="rectangular" width="100%" height={60} sx={{ mt: 2 }} />
    </Paper>
  </Box>
));

const TestCaseDisplay = ({ testCases = [] }) => {
  const [isLoading] = useState(false);

  // 使用useMemo缓存计算结果
  const memoizedTestCases = useMemo(() => testCases, [testCases]);

  // 虚拟滚动的行渲染器
  const Row = useCallback(({ index, style }) => {
    if (isLoading) {
      return <TestCaseSkeleton />;
    }

    const testCase = memoizedTestCases[index];
    if (!testCase) {
      return <TestCaseSkeleton />;
    }

    return (
      <TestCaseItem
        testCase={testCase}
        index={index}
        style={style}
      />
    );
  }, [memoizedTestCases, isLoading]);

  // 生成Markdown格式的测试用例（保持原有功能）
  const generateMarkdown = useMemo(() => {
    if (!memoizedTestCases || memoizedTestCases.length === 0) return '';

    let markdown = '# 生成的测试用例\n\n';

    memoizedTestCases.forEach((testCase, index) => {
      markdown += `## ${testCase.id || `TC-${index + 1}`}: ${testCase.title}\n\n`;

      if (testCase.priority) {
        markdown += `**优先级:** ${testCase.priority}\n\n`;
      }

      markdown += `**描述:** ${testCase.description}\n\n`;

      if (testCase.preconditions) {
        markdown += `**前置条件:** ${testCase.preconditions}\n\n`;
      }

      markdown += `### 测试步骤\n\n`;
      markdown += `| # | 步骤描述 | 预期结果 |\n`;
      markdown += `| --- | --- | --- |\n`;

      testCase.steps.forEach(step => {
        markdown += `| ${step.step_number} | ${step.description} | ${step.expected_result} |\n`;
      });

      markdown += '\n\n';
    });

    return markdown;
  }, [memoizedTestCases]);

  // 决定使用哪种渲染方式
  const shouldUseVirtualScroll = memoizedTestCases.length > 10;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Box sx={{ mb: 2 }}>
        <Typography variant="body1" color="text.secondary">
          已生成 {memoizedTestCases.length} 个测试用例
          {shouldUseVirtualScroll && (
            <Typography variant="caption" display="block" color="text.secondary">
              使用虚拟滚动优化性能
            </Typography>
          )}
        </Typography>
      </Box>

      {shouldUseVirtualScroll ? (
        // 大量数据时使用虚拟滚动
        <Box sx={{
          flexGrow: 1,
          bgcolor: '#f8f9fa',
          borderRadius: 1,
          overflow: 'hidden'
        }}>
          <List
            height={600}
            itemCount={memoizedTestCases.length}
            itemSize={120} // 每个项目的估计高度
            width="100%"
          >
            {Row}
          </List>
        </Box>
      ) : (
        // 少量数据时使用原有的Markdown渲染
        <Box sx={{
          flexGrow: 1,
          overflow: 'auto',
          bgcolor: '#f8f9fa',
          p: 3,
          borderRadius: 1,
          maxHeight: '600px'
        }}>
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {generateMarkdown}
          </ReactMarkdown>
        </Box>
      )}
    </Box>
  );
};

export default TestCaseDisplay;
