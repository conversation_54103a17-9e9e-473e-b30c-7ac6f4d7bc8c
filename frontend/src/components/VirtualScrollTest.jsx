import React from 'react';
import TestCaseDisplay from './TestCaseDisplay';

// 生成测试数据
const generateTestData = (count = 15) => {
  const testCases = [];
  for (let i = 0; i < count; i++) {
    testCases.push({
      id: `TC-${i + 1}`,
      title: `测试用例 ${i + 1} - ${i % 3 === 0 ? '长标题测试用例，包含更多详细信息' : '标准测试用例'}`,
      description: `这是第 ${i + 1} 个测试用例的详细描述。${i % 2 === 0 ? '这个测试用例包含更多的描述内容，用于测试展开后的高度变化。' : '简短描述。'}`,
      priority: i % 3 === 0 ? '高' : i % 3 === 1 ? '中' : '低',
      preconditions: i % 4 === 0 ? `前置条件 ${i + 1}：需要准备测试环境和数据` : null,
      steps: Array.from({ length: Math.floor(Math.random() * 5) + 2 }, (_, stepIndex) => ({
        step_number: stepIndex + 1,
        description: `步骤 ${stepIndex + 1}：执行操作 ${stepIndex + 1}${stepIndex % 2 === 0 ? '，这是一个较长的步骤描述，用于测试表格在展开时的高度计算' : ''}`,
        expected_result: `预期结果 ${stepIndex + 1}：系统应该正确响应${stepIndex % 3 === 0 ? '，并显示相应的成功信息' : ''}`
      }))
    });
  }
  return testCases;
};

const VirtualScrollTest = () => {
  const testData = generateTestData(15);

  return (
    <div style={{ padding: '20px', height: '100vh' }}>
      <h2>虚拟滚动测试 - 修复展开重叠问题</h2>
      <p>测试说明：展开任意一个测试用例，检查是否还有样式重叠问题</p>
      <TestCaseDisplay testCases={testData} />
    </div>
  );
};

export default VirtualScrollTest;
